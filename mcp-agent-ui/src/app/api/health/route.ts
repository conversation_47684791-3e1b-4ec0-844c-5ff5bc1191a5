import { NextRequest } from 'next/server';
import { MCPChatService } from '@/lib/mcp-chat-service';

export async function GET(req: NextRequest) {
  try {
    const mcpService = new MCPChatService();
    const healthStatus = await mcpService.getHealthStatus();
    
    const status = healthStatus.healthy ? 200 : 503;
    
    return new Response(JSON.stringify({
      timestamp: new Date().toISOString(),
      service: 'MCP Chat Service',
      ...healthStatus,
    }), {
      status,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    return new Response(JSON.stringify({
      timestamp: new Date().toISOString(),
      service: 'MCP Chat Service',
      status: 'error',
      healthy: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }
}
