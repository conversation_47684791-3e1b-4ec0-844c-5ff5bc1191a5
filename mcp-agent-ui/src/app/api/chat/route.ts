import { NextRequest } from 'next/server';
import { MCPChatService } from '@/lib/mcp-chat-service';

// Allow streaming responses up to 30 seconds
export const maxDuration = 30;

export async function POST(req: NextRequest) {
  try {
    const { messages } = await req.json();

    // Extract the latest user message
    const latestMessage = messages[messages.length - 1];
    if (!latestMessage || latestMessage.role !== 'user') {
      return new Response('Invalid message format', { status: 400 });
    }

    // Initialize MCP Chat Service
    const mcpService = new MCPChatService();

    // Get streaming response from MCP Multi-Agent
    const response = await mcpService.streamChat(latestMessage.content, {
      conversationHistory: messages.slice(0, -1), // All messages except the latest
      enableToolVisibility: true,
      maxSteps: 5,
    });

    return response;
  } catch (error) {
    console.error('MCP Chat API error:', error);
    return new Response(
      JSON.stringify({
        error: 'Internal Server Error',
        details: error instanceof Error ? error.message : 'Unknown error'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
